#include "usart_app.h"

typedef enum
{
    OTHER_CMD = 0,
    RTC_CONFIG,
    RATIO_CONFIG,
    LIMIT_CONFIG,
} UsartStatus_t;

UsartStatus_t usart_status = OTHER_CMD;

static void Cmd_Conf(void)
{
    FIL config_file;
    FRESULT result = FR_OK;
    BYTE buffer[128] = {0};
    UINT bytes_read = 0;

    result = f_open(&config_file, "0:/config.ini", FA_OPEN_EXISTING | FA_READ);
    if (result != FR_OK)
    {
        Usart0Printf("config.ini file not found\n");
        return;
    }

    result = f_read(&config_file, buffer, sizeof(buffer), &bytes_read);
    if (result != FR_OK)
    {
        Usart0Printf("config.ini file not found\n");
        return;
    }
    f_close(&config_file);

    Usart0Printf("%s\n", buffer);
    sscanf((char *)buffer, "[Ratio]\nCh0 = %f\n\n[Limit]\nCh0 = %f", &config_data.ratio, &config_data.limit);

    Flash_WriteConfig();

    // Usart0Printf("Ratio = %.2f\n", config_data.ratio);
    // Usart0Printf("Limit = %.2f\n", config_data.limit);
    Usart0Printf("Limit = %.2f\n", adc0_voltage);
    Usart0Printf("config read success\n");
}

static void Cmd_RtcNow(void)
{
    Usart0Printf("Current Time: 20%hhu-%02hhu-%02hhu %02hhu:%02hhu:%02hhu\n",
                ucRtc.year, ucRtc.month, ucRtc.date, ucRtc.hour, 
                ucRtc.minute, ucRtc.second);
}

static void Cmd_RtcConfig(char *buffer)
{
    rtc_parameter_struct current_time = {0};
    int chars_read;

    int result = sscanf(buffer, "20%hhu-%hhu-%hhu %hhu:%hhu:%hhu\n%n", 
                        &current_time.year, &current_time.month, &current_time.date, &current_time.hour, 
                        &current_time.minute, &current_time.second, &chars_read);
    if (result == 6 && chars_read == strlen(buffer))
    {
        SetRtc(&current_time);
        Usart0Printf("RTC Config: success\n");
        Usart0Printf("Time: 20%hhu-%02hhu-%02hhu %02hhu:%02hhu:%02hhu\n",
                     current_time.year, current_time.month, current_time.date, current_time.hour, 
                     current_time.minute, current_time.second);
        // TF_WriteLog(LOG_RTC_CONFIG_OK);
    }
}

static void Cmd_TestProc(void)
{
    ErrStatus test_status = SUCCESS;
    DSTATUS disk_status = 0;
    Usart0Printf("======system selftest======\n");

    if (flash_id == 0xc84013) Usart0Printf("flash............ok\n");
    else 
    {
        Usart0Printf("flash............error\n");
        test_status = ERROR;
        // TF_WriteLog(LOG_FLASH_ERROR);
    }
    disk_status = disk_initialize(0);
    if (disk_status == 0) Usart0Printf("TF card...........ok\n");
    else 
    {
        Usart0Printf("TF card...........error\n");
        test_status = ERROR;
        // TF_WriteLog(LOG_TFCARD_ERROR);
    }
    if (flash_id == 0xc84013) Usart0Printf("flash ID: 0x%lx\n", flash_id);
    else Usart0Printf("can not find flash\n");
    if (disk_status == 0) Usart0Printf("TF card memory: %uKB\n", sd_card_capacity_get());
    else Usart0Printf("can not find TF card\n");

    Usart0Printf("RTC: 20%02d-%02d-%02d %02d:%02d:%02d\n", ucRtc.year, ucRtc.month, ucRtc.date, ucRtc.hour, ucRtc.minute, ucRtc.second);
    Usart0Printf("======system selftest======\n");

    // if (test_status == SUCCESS) TF_WriteLog(LOG_TEST_OK);
}

static void CmdProc(char *buffer)
{
    if (usart_status == RTC_CONFIG)
    {
        Cmd_RtcConfig(buffer);
        usart_status = OTHER_CMD;
        return;
    }
    else
    {
        if (strcmp(buffer, "test\n") == 0)
        {
            // TF_WriteLog(LOG_SYSTEM_TEST);
            Cmd_TestProc();
        }
        else if (strcmp(buffer, "RTC Config\n") == 0)
        {
            usart_status = RTC_CONFIG;
            // TF_WriteLog(LOG_RTC_CONFIG);
        }
        else if (strcmp(buffer, "RTC now\n") == 0) Cmd_RtcNow();
        else if (strcmp(buffer, "conf\n") == 0) Cmd_Conf();
    }
}

void Usart0Task(void)
{
    if (usart0_rx_flag == 0) return;

    CmdProc((char *)usart0_rx_buffer_proc);

    memset(usart0_rx_buffer_proc, 0, USART0_BUFFER_SIZE);
    usart0_rx_flag = 0;
}
