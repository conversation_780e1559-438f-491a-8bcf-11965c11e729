#include "usart_app.h"
#include <stdlib.h>  // for atof function

typedef enum
{
    OTHER_CMD = 0,
    RTC_CONFIG,
    RATIO_CONFIG,
    LIMIT_CONFIG,
    DEVICE_ID_CONFIG
} UsartStatus_t;

UsartStatus_t usart_status = OTHER_CMD;

static void Cmd_DeviceIdConfig(char *buffer)
{
    uint8_t input_buffer[32] = {0};
    int chars_read;

    int result = sscanf(buffer, "%s\n%n", input_buffer, &chars_read);
    if (result == 1 && chars_read == strlen(buffer))
    {
        Flash_WriteDeviceId(input_buffer);

        Usart0Printf("device_id modified success\n");
        Usart0Printf("device_id: %s\n", input_buffer);
    }
}

void Cmd_Hide(void)
{
    sample_status.hide = 1;
}

void Cmd_UnHide(void)
{
    sample_status.hide = 0;
}

void Cmd_PowerReset(void)
{
    power_count = 0;
    Flash_WritePower();
    Usart0Printf("Power reset success!\n");
}

void Cmd_Start(void)
{
    Usart0Printf("Periodic Sampling\n");
    Usart0Printf("sample cycle: %ds\n", sample_cycle);
    sample_status.enable = 1;
    OLED_Clear();
    SampleProc();
}

void Cmd_Stop(void)
{
    Usart0Printf("Periodic Sampling STOP\n");

    sample_status.enable = 0;

    OLED_Clear();
    OledDrawStr(0, 0, "system idle");
}

static void Cmd_ConfigSave(void)
{
    Usart0Printf("ratio: %.2f\n", config_data.ratio);
    Usart0Printf("limit: %.2f\n", config_data.limit);

    Flash_WriteConfig();
    Usart0Printf("save parameters to flash\n");
}

static void Cmd_ConfigRead(void)
{
    Flash_ReadConfig();
    Usart0Printf("read parameters from flash\n");
    Usart0Printf("ratio: %.2f\n", config_data.ratio);
    Usart0Printf("limit: %.2f\n", config_data.limit);
}

static void Cmd_Limit(char *buffer)
{
    float limit_input = 0.0f;
    int chars_read;

    int result = sscanf(buffer, "%f\n%n", &limit_input, &chars_read);
    if (result == 1 && chars_read == strlen(buffer))
    {
        if (limit_input >= 0.0f && limit_input <= 500.0f)
        {
            config_data.limit = limit_input;
            Usart0Printf("limit modified success\n");
            Usart0Printf("Limit = %.2f\n", config_data.limit);

            TF_WriteLog(LOG_LIMIT_CONFIG_OK);
        }
        else
        {
            Usart0Printf("limit invalid\n");
            Usart0Printf("Limit = %.2f\n", config_data.limit);
        }
    }
    else
    {
        Usart0Printf("limit invalid\n");
        Usart0Printf("Limit = %.2f\n", config_data.limit);
    }
}

static void Cmd_Ratio(char *buffer)
{
    float ratio_input = 0.0f;
    int chars_read;

    int result = sscanf(buffer, "%f\n%n", &ratio_input, &chars_read);
    if (result == 1 && chars_read == strlen(buffer))
    {
        if (ratio_input >= 0.0f && ratio_input <= 100.0f)
        {
            config_data.ratio = ratio_input;
            Usart0Printf("ratio modified success\n");
            Usart0Printf("Ratio = %.2f\n", config_data.ratio);

            TF_WriteLog(LOG_RATIO_CONFIG_OK);
        }
        else
        {
            Usart0Printf("ratio invalid\n");
            Usart0Printf("Ratio = %.2f\n", config_data.ratio);
        }
    }
    else
    {
        Usart0Printf("\nratio invalid\n");
        Usart0Printf("Ratio = %.2f\n", config_data.ratio);
    }
}

static void Cmd_Conf(void)
{
    FIL config_file;
    FRESULT result = FR_OK;
    BYTE buffer[128] = {0};
    UINT bytes_read = 0;

    result = f_open(&config_file, "0:/config.ini", FA_OPEN_EXISTING | FA_READ);
    if (result != FR_OK)
    {
        Usart0Printf("config.ini file not found\n");
        return;
    }

    result = f_read(&config_file, buffer, sizeof(buffer), &bytes_read);
    if (result != FR_OK)
    {
        Usart0Printf("config.ini file not found\n");
        return;
    }
    f_close(&config_file);

    sscanf((char *)buffer, "[Ratio]\nCh0 = %f\n\n[Limit]\nCh0 = %f", &config_data.ratio, &config_data.limit);

    Flash_WriteConfig();

    Usart0Printf("Ratio = %.2f\n", config_data.ratio);
    Usart0Printf("Limit = %.2f\n", config_data.limit);
    Usart0Printf("config read success\n");
}

static void Cmd_RtcNow(void)
{
    Usart0Printf("Current Time: 20%hhu-%02hhu-%02hhu %02hhu:%02hhu:%02hhu\n",
                ucRtc.year, ucRtc.month, ucRtc.date, ucRtc.hour, 
                ucRtc.minute, ucRtc.second);
}

static void Cmd_RtcConfig(char *buffer)
{
    rtc_parameter_struct current_time = {0};
    int chars_read;

    int result = sscanf(buffer, "20%hhu-%hhu-%hhu %hhu:%hhu:%hhu\n%n", 
                        &current_time.year, &current_time.month, &current_time.date, &current_time.hour, 
                        &current_time.minute, &current_time.second, &chars_read);
    if (result == 6 && chars_read == strlen(buffer))
    {
        SetRtc(&current_time);
        Usart0Printf("RTC Config: success\n");
        Usart0Printf("Time: 20%hhu-%02hhu-%02hhu %02hhu:%02hhu:%02hhu\n",
                     current_time.year, current_time.month, current_time.date, current_time.hour, 
                     current_time.minute, current_time.second);
        TF_WriteLog(LOG_RTC_CONFIG_OK);
    }
}

static void Cmd_TestProc(void)
{
    ErrStatus test_status = SUCCESS;
    DSTATUS disk_status = 0;
    Usart0Printf("======system selftest======\n");

    if (flash_id == 0xc84013) Usart0Printf("flash............ok\n");
    else 
    {
        Usart0Printf("flash............error\n");
        test_status = ERROR;
        TF_WriteLog(LOG_FLASH_ERROR);
    }
    disk_status = disk_initialize(0);
    if (disk_status == 0) Usart0Printf("TF card...........ok\n");
    else 
    {
        Usart0Printf("TF card...........error\n");
        test_status = ERROR;
        TF_WriteLog(LOG_TFCARD_ERROR);
    }
    if (flash_id == 0xc84013) Usart0Printf("flash ID: 0x%lx\n", flash_id);
    else Usart0Printf("can not find flash\n");
    if (disk_status == 0) Usart0Printf("TF card memory: %uKB\n", sd_card_capacity_get());
    else Usart0Printf("can not find TF card\n");

    Usart0Printf("RTC: 20%02d-%02d-%02d %02d:%02d:%02d\n", ucRtc.year, ucRtc.month, ucRtc.date, ucRtc.hour, ucRtc.minute, ucRtc.second);
    Usart0Printf("======system selftest======\n");

    if (test_status == SUCCESS) TF_WriteLog(LOG_TEST_OK);
}

static void CmdProc(char *buffer)
{
    if (usart_status == RTC_CONFIG)
    {
        Cmd_RtcConfig(buffer);
        usart_status = OTHER_CMD;
        return;
    }
    else if (usart_status == RATIO_CONFIG)
    {
        Cmd_Ratio(buffer);
        usart_status = OTHER_CMD;
        return;
    }
    else if (usart_status == LIMIT_CONFIG)
    {
        Cmd_Limit(buffer);
        usart_status = OTHER_CMD;
        return;
    }
    else if (usart_status == DEVICE_ID_CONFIG)
    {
        Cmd_DeviceIdConfig(buffer);
        usart_status = OTHER_CMD;
        return;
    }
    else
    {
        if (strcmp(buffer, "test\n") == 0)
        {
            TF_WriteLog(LOG_SYSTEM_TEST);
            Cmd_TestProc();
        }
        else if (strcmp(buffer, "RTC Config\n") == 0)
        {
            usart_status = RTC_CONFIG;
            TF_WriteLog(LOG_RTC_CONFIG);
        }
        else if (strcmp(buffer, "RTC now\n") == 0) Cmd_RtcNow();
        else if (strcmp(buffer, "conf\n") == 0) Cmd_Conf();
        else if (strcmp(buffer, "ratio\n") == 0)
        {
            Usart0Printf("Ratio = %.2f\n", config_data.ratio);
            Usart0Printf("Input value(0~100):\n");
            usart_status = RATIO_CONFIG;

            TF_WriteLog(LOG_RATIO_CONFIG);
        }
        else if (strcmp(buffer, "limit\n") == 0)
        {
            Usart0Printf("Limit = %.2f\n", config_data.limit);
            Usart0Printf("Input value(0~500):\n");
            usart_status = LIMIT_CONFIG;

            TF_WriteLog(LOG_LIMIT_CONFIG);
        }
        else if (strcmp(buffer, "config save\n") == 0) Cmd_ConfigSave();
        else if (strcmp(buffer, "config read\n") == 0) Cmd_ConfigRead();
        else if (strcmp(buffer, "start\n") == 0) 
        {
            Cmd_Start();
            TF_WriteLog(LOG_SAMPLE_START_USART);
        }
        else if (strcmp(buffer, "stop\n") == 0)
        {
            Cmd_Stop();
            TF_WriteLog(LOG_SAMPLE_STOP_USART);
        }
        else if (strcmp(buffer, "hide\n") == 0)
        {
            Cmd_Hide();
            TF_WriteLog(LOG_HIDE_DATA);
        }
        else if (strcmp(buffer, "unhide\n") == 0) 
        {
            Cmd_UnHide();
            TF_WriteLog(LOG_UNHIDE_DATA);
        }
        else if (strcmp(buffer, "power reset\n") == 0) Cmd_PowerReset();
        else if (strcmp(buffer, "device_id\n") == 0)
        {
            Usart0Printf("device_id:\n");
            usart_status = DEVICE_ID_CONFIG;
        }
    }
}

void Usart0Task(void)
{
    if (usart0_rx_flag == 0) return;

    CmdProc((char *)usart0_rx_buffer_proc);

    memset(usart0_rx_buffer_proc, 0, USART0_BUFFER_SIZE);
    usart0_rx_flag = 0;
}

// RS485通讯协议处理函数
static void Rs485CmdProc(char *buffer)
{
    // 去除字符串末尾的换行符和回车符
    int len = strlen(buffer);
    while (len > 0 && (buffer[len-1] == '\n' || buffer[len-1] == '\r')) {
        buffer[--len] = '\0';
    }

    // 处理各种RS485命令
    if (strcmp(buffer, "test") == 0) {
        Usart1Printf("RS485 OK\n");
    }
    else if (strcmp(buffer, "status") == 0) {
        Usart1Printf("Device_ID:2025-CIMC-%s\n", device_id);
        Usart1Printf("Voltage:%.2fV\n", adc0_voltage * config_data.ratio);
        Usart1Printf("Status:%s\n", sample_status.enable ? "Running" : "Idle");
    }
    else if (strcmp(buffer, "data") == 0) {
        Usart1Printf("20%02d-%02d-%02d %02d:%02d:%02d,%.2fV\n",
                    ucRtc.year, ucRtc.month, ucRtc.date,
                    ucRtc.hour, ucRtc.minute, ucRtc.second,
                    adc0_voltage * config_data.ratio);
    }
    else if (strcmp(buffer, "start") == 0) {
        sample_status.enable = 1;
        Usart1Printf("Sampling Started\n");
    }
    else if (strcmp(buffer, "stop") == 0) {
        sample_status.enable = 0;
        Usart1Printf("Sampling Stopped\n");
    }
    else if (strncmp(buffer, "ratio=", 6) == 0) {
        float new_ratio = atof(buffer + 6);
        if (new_ratio > 0 && new_ratio <= 100) {
            config_data.ratio = new_ratio;
            Usart1Printf("Ratio set to %.2f\n", config_data.ratio);
        } else {
            Usart1Printf("Invalid ratio value\n");
        }
    }
    else if (strncmp(buffer, "limit=", 6) == 0) {
        float new_limit = atof(buffer + 6);
        if (new_limit > 0 && new_limit <= 500) {
            config_data.limit = new_limit;
            Usart1Printf("Limit set to %.2f\n", config_data.limit);
        } else {
            Usart1Printf("Invalid limit value\n");
        }
    }
    else {
        // 未知命令，回显接收到的数据
        Usart1Printf("Unknown command: %s\n", buffer);
    }
}

void Usart1Task(void)
{
    if (usart1_rx_flag == 0) return;

    // 处理RS485命令
    Rs485CmdProc((char *)usart1_rx_buffer_proc);
    Usart1Printf("%s\n", usart1_rx_buffer_proc);

    memset(usart1_rx_buffer_proc, 0, USART1_BUFFER_SIZE);
    usart1_rx_flag = 0;
}
