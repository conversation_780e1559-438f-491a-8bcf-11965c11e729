#include "adc_app.h"

void AdcTask(void)
{
    uint32_t adc_sum = 0;

    for (uint8_t i = 0; i < 32; i++)
    {
        adc_sum += adc0_value[i];
    }

    adc0_voltage = (float)adc_sum / 32.0f * 3.3f / 4095.0f;
}

uint32_t HideVoltageProc(void)
{
    float total_value = adc0_voltage * config_data.ratio;
    
    // 整数部分: 2字节
    uint16_t integer_part = (uint16_t)floor(total_value);
    
    // 小数部分: 小数 * 65536 转换为2字节
    float decimal_fraction = total_value - floor(total_value);
    uint16_t decimal_part = (uint16_t)(decimal_fraction * 65536.0f);
    
    return (integer_part << 16) | decimal_part;
}


void SampleProc(void)
{
    sample_status.last_sample_time = ucRtc.second;

    if (sample_status.hide == 0)
    {
        Usart0Printf("20%02d-%02d-%02d %02d:%02d:%02d ", 
                    ucRtc.year, ucRtc.month, ucRtc.date, ucRtc.hour, ucRtc.minute, ucRtc.second);
        if (adc0_voltage * config_data.ratio <= config_data.limit)
        {
            Usart0Printf("ch0=%.2fV\n", adc0_voltage * config_data.ratio);
            ucLed[1] = 0;
            TF_WriteSample();
        }
        else
        {
            Usart0Printf("ch0=%.2fV OverLimit(%.2f)!\n", adc0_voltage * config_data.ratio, config_data.limit);
            ucLed[1] = 1;
            TF_WriteOverLimit();
        }
    }
    else
    {
        Usart0Printf("%08lx", (uint32_t)HideTimeProc());
        if (adc0_voltage * config_data.ratio <= config_data.limit)
        {
            Usart0Printf("%08lx\n", HideVoltageProc());
            ucLed[1] = 0;
            TF_WriteHideData();
        }
        else
        {
            Usart0Printf("%08lx*\n", HideVoltageProc());
            ucLed[1] = 1;
            TF_WriteOverLimit();
        }
    }

    OledDrawStr(0, 0, "%02d:%02d:%02d ", ucRtc.hour, ucRtc.minute, ucRtc.second);
    OledDrawStr(0, 2, "%.2fV ", adc0_voltage * config_data.ratio);
}

void SampleTask(void)
{
    if (sample_status.enable == 0)
    {
        return;
    }

    if (ucRtc.second == (sample_status.last_sample_time + sample_cycle) % 60)
    {
        SampleProc();
    }
}
