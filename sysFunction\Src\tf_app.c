#include "tf_app.h"

FIL sample_file;    // Sample文件对象
FIL overLimit_file; // OverLimit文件对象
FIL hide_file;      // Hide文件对象
FIL log_file;       // Log文件对象

void TF_WriteSample(void)
{
    FRESULT result = FR_OK;

    if (storage_param.sample_file_open == 0 || storage_param.sample_cmd_count >= 10)
    {
        if (storage_param.sample_file_open == 1)
        {
            f_close(&sample_file);
            storage_param.sample_file_open = 0;
        }

        sprintf(storage_param.sample_file_name, "0:/sample/sampleData20%02d%02d%02d%02d%02d%02d.txt", 
                ucRtc.year, ucRtc.month, ucRtc.date, ucRtc.hour, ucRtc.minute, ucRtc.second);
        
        result = f_open(&sample_file, storage_param.sample_file_name, FA_OPEN_ALWAYS | FA_WRITE);
        if (result != FR_OK)
        {
            // Usart0Printf("Failed to open sample file: %s\n", storage_param.sample_file_name);
            return;
        }
        storage_param.sample_file_open = 1;
        storage_param.sample_cmd_count = 0;
    }

    char write_buffer[512] = {0};
    UINT bytes_write = 0;

    sprintf(write_buffer, "20%02d-%02d-%02d %02d:%02d:%02d %.1fV\r\n",
            ucRtc.year, ucRtc.month, ucRtc.date, ucRtc.hour, ucRtc.minute, ucRtc.second, adc0_voltage * config_data.ratio);
    
    result = f_write(&sample_file, write_buffer, strlen(write_buffer), &bytes_write);

    if (result != FR_OK || bytes_write != strlen(write_buffer))
    {
        // Usart0Printf("Failed to write to sample file: %s\n", storage_param.sample_file_name);
        return;
    }
    f_sync(&sample_file);
    storage_param.sample_cmd_count++;
}

void TF_WriteOverLimit(void)
{
    FRESULT result = FR_OK;

    if (storage_param.overLimit_file_open == 0 || storage_param.overLimit_cmd_count >= 10)
    {
        if (storage_param.overLimit_file_open == 1)
        {
            f_close(&overLimit_file);
            storage_param.overLimit_file_open = 0;
        }
        
        sprintf(storage_param.overLimit_file_name, "0:/overLimit/overLimit20%02d%02d%02d%02d%02d%02d.txt", 
                ucRtc.year, ucRtc.month, ucRtc.date, ucRtc.hour, ucRtc.minute, ucRtc.second);

        result = f_open(&overLimit_file, storage_param.overLimit_file_name, FA_OPEN_ALWAYS | FA_WRITE);
        if (result != FR_OK)
        {
            // Usart0Printf("Failed to open overLimit file: %s\n", storage_param.overLimit_file_name);
            return;
        }
        storage_param.overLimit_file_open = 1;
        storage_param.overLimit_cmd_count = 0;
    }

    char write_buffer[512] = {0};
    UINT bytes_write = 0;

    sprintf(write_buffer, "20%02d-%02d-%02d %02d:%02d:%02d %.1fV limit %.2fV\r\n",
            ucRtc.year, ucRtc.month, ucRtc.date, ucRtc.hour, ucRtc.minute, ucRtc.second, adc0_voltage * config_data.ratio, config_data.limit);
    
    result = f_write(&overLimit_file, write_buffer, strlen(write_buffer), &bytes_write);

    if (result != FR_OK || bytes_write != strlen(write_buffer))
    {
        // Usart0Printf("Failed to write to overLimit file: %s\n", storage_param.overLimit_file_name);
        return;
    }
    f_sync(&overLimit_file);
    storage_param.overLimit_cmd_count++;
}

void TF_WriteHideData(void)
{
    FRESULT result = FR_OK;

    if (storage_param.hide_file_open == 0 || storage_param.hide_cmd_count >= 10)
    {
        if (storage_param.hide_file_open == 1)
        {
            f_close(&hide_file);
            storage_param.hide_file_open = 0;
        }

        sprintf(storage_param.hide_file_name, "0:/hideData/hideData20%02d%02d%02d%02d%02d%02d.txt", 
                ucRtc.year, ucRtc.month, ucRtc.date, ucRtc.hour, ucRtc.minute, ucRtc.second);

        result = f_open(&hide_file, storage_param.hide_file_name, FA_OPEN_ALWAYS | FA_WRITE);

        if (result != FR_OK)
        {
            // Usart0Printf("Failed to open hide file: %s\n", storage_param.hide_file_name);
            return;
        }
        storage_param.hide_file_open = 1;
        storage_param.hide_cmd_count = 0;
    }

    char write_buffer[512] = {0};
    UINT bytes_write = 0;

    sprintf(write_buffer, "20%02d-%02d-%02d %02d:%02d:%02d %.1fV\r\n",
            ucRtc.year, ucRtc.month, ucRtc.date, ucRtc.hour, ucRtc.minute, ucRtc.second, adc0_voltage * config_data.ratio);
    result = f_write(&hide_file, write_buffer, strlen(write_buffer), &bytes_write);

    if (result != FR_OK || bytes_write != strlen(write_buffer))
    {
        // Usart0Printf("Failed to write to hide file: %s\n", storage_param.hide_file_name);
        return;
    }
    f_sync(&hide_file);

    memset(write_buffer, 0, sizeof(write_buffer));
    bytes_write = 0;
    sprintf(write_buffer, "hide: %08lx%08x\r\n", (uint32_t)HideTimeProc(), HideVoltageProc());
    result = f_write(&hide_file, write_buffer, strlen(write_buffer), &bytes_write);

    if (result != FR_OK || bytes_write != strlen(write_buffer))
    {
        // Usart0Printf("Failed to write to hide file: %s\n", storage_param.hide_file_name);
        return;
    }
    f_sync(&hide_file);
    storage_param.hide_cmd_count++;
}

void TF_WriteLog(LogType_t log_type)
{
    FRESULT result = FR_OK;

    if (storage_param.log_file_open == 0)
    {
        sprintf(storage_param.log_file_name, "0:/log/log%d.txt", power_count);

        result = f_open(&log_file, storage_param.log_file_name, FA_CREATE_ALWAYS | FA_WRITE);

        if (result != FR_OK)
        {
            // Usart0Printf("Failed to open log file: %s\n", storage_param.log_file_name);
            return;
        }
        storage_param.log_file_open = 1;
    }

    char write_buffer[512] = {0};
    UINT bytes_write = 0;

    switch(log_type)
    {
        case LOG_SYSTEM_INIT:
            sprintf(write_buffer, "20%02d-%02d-%02d %02d:%02d:%02d system init\r\n",
                    ucRtc.year, ucRtc.month, ucRtc.date, ucRtc.hour, ucRtc.minute, ucRtc.second);
            result = f_write(&log_file, write_buffer, strlen(write_buffer), &bytes_write);
            if (result != FR_OK || bytes_write != strlen(write_buffer))
            {
                // Usart0Printf("Failed to write to log file: %s\n", storage_param.log_file_name);
                return;
            }
            f_sync(&log_file);
            break;
        
        case LOG_RTC_CONFIG:
            sprintf(write_buffer, "20%02d-%02d-%02d %02d:%02d:%02d rtc config\r\n",
                ucRtc.year, ucRtc.month, ucRtc.date, ucRtc.hour, ucRtc.minute, ucRtc.second);
            result = f_write(&log_file, write_buffer, strlen(write_buffer), &bytes_write);
            if (result != FR_OK || bytes_write != strlen(write_buffer))
            {
                // Usart0Printf("Failed to write to log file: %s\n", storage_param.log_file_name);
                return;
            }
            f_sync(&log_file);
            break;

        case LOG_RTC_CONFIG_OK:
            sprintf(write_buffer, "20%02d-%02d-%02d %02d:%02d:%02d rtc config success to 20%02d-%02d-%02d %02d:%02d:%02d\r\n",
                ucRtc.year, ucRtc.month, ucRtc.date, ucRtc.hour, ucRtc.minute, ucRtc.second, ucRtc.year, ucRtc.month, ucRtc.date, ucRtc.hour, ucRtc.minute, ucRtc.second);
            result = f_write(&log_file, write_buffer, strlen(write_buffer), &bytes_write);
            if (result != FR_OK || bytes_write != strlen(write_buffer))
            {
                // Usart0Printf("Failed to write to log file: %s\n", storage_param.log_file_name);
                return;
            }
            f_sync(&log_file);
            break;
        
        case LOG_SYSTEM_TEST:
            sprintf(write_buffer, "20%02d-%02d-%02d %02d:%02d:%02d system hardware test\r\n",
                ucRtc.year, ucRtc.month, ucRtc.date, ucRtc.hour, ucRtc.minute, ucRtc.second);
            result = f_write(&log_file, write_buffer, strlen(write_buffer), &bytes_write);
            if (result != FR_OK || bytes_write != strlen(write_buffer))
            {
                // Usart0Printf("Failed to write to log file: %s\n", storage_param.log_file_name);
                return;
            }
            f_sync(&log_file);
            break;

        case LOG_TEST_OK:
            sprintf(write_buffer, "20%02d-%02d-%02d %02d:%02d:%02d test ok\r\n",
                ucRtc.year, ucRtc.month, ucRtc.date, ucRtc.hour, ucRtc.minute, ucRtc.second);
            result = f_write(&log_file, write_buffer, strlen(write_buffer), &bytes_write);
            if (result != FR_OK || bytes_write != strlen(write_buffer))
            {
                // Usart0Printf("Failed to write to log file: %s\n", storage_param.log_file_name);
                return;
            }
            f_sync(&log_file);
            break;

        case LOG_FLASH_ERROR:
            sprintf(write_buffer, "20%02d-%02d-%02d %02d:%02d:%02d test error: flash not found\r\n",
                ucRtc.year, ucRtc.month, ucRtc.date, ucRtc.hour, ucRtc.minute, ucRtc.second);
            result = f_write(&log_file, write_buffer, strlen(write_buffer), &bytes_write);
            if (result != FR_OK || bytes_write != strlen(write_buffer))
            {
                // Usart0Printf("Failed to write to log file: %s\n", storage_param.log_file_name);
                return;
            }
            f_sync(&log_file);
            break;

        case LOG_TFCARD_ERROR:
            sprintf(write_buffer, "20%02d-%02d-%02d %02d:%02d:%02d test error: tf card not found\r\n",
                ucRtc.year, ucRtc.month, ucRtc.date, ucRtc.hour, ucRtc.minute, ucRtc.second);
            result = f_write(&log_file, write_buffer, strlen(write_buffer), &bytes_write);
            if (result != FR_OK || bytes_write != strlen(write_buffer))
            {
                // Usart0Printf("Failed to write to log file: %s\n", storage_param.log_file_name);
                return;
            }
            f_sync(&log_file);
            break;

        case LOG_RATIO_CONFIG:
            sprintf(write_buffer, "20%02d-%02d-%02d %02d:%02d:%02d ratio config\r\n",
                ucRtc.year, ucRtc.month, ucRtc.date, ucRtc.hour, ucRtc.minute, ucRtc.second);
            result = f_write(&log_file, write_buffer, strlen(write_buffer), &bytes_write);
            if (result != FR_OK || bytes_write != strlen(write_buffer))
            {
                // Usart0Printf("Failed to write to log file: %s\n", storage_param.log_file_name);
                return;
            }
            f_sync(&log_file);
            break;

        case LOG_RATIO_CONFIG_OK:
            sprintf(write_buffer, "20%02d-%02d-%02d %02d:%02d:%02d ratio config success to %.2f\r\n",
                ucRtc.year, ucRtc.month, ucRtc.date, ucRtc.hour, ucRtc.minute, ucRtc.second, config_data.ratio);
            result = f_write(&log_file, write_buffer, strlen(write_buffer), &bytes_write);
            if (result != FR_OK || bytes_write != strlen(write_buffer))
            {
                // Usart0Printf("Failed to write to log file: %s\n", storage_param.log_file_name);
                return;
            }
            f_sync(&log_file);
            break;

        case LOG_LIMIT_CONFIG:
            sprintf(write_buffer, "20%02d-%02d-%02d %02d:%02d:%02d limit config\r\n",
                ucRtc.year, ucRtc.month, ucRtc.date, ucRtc.hour, ucRtc.minute, ucRtc.second);
            result = f_write(&log_file, write_buffer, strlen(write_buffer), &bytes_write);
            if (result != FR_OK || bytes_write != strlen(write_buffer))
            {
                // Usart0Printf("Failed to write to log file: %s\n", storage_param.log_file_name);
                return;
            }
            f_sync(&log_file);
            break;

        case LOG_LIMIT_CONFIG_OK:
            sprintf(write_buffer, "20%02d-%02d-%02d %02d:%02d:%02d limit config success to %.2f\r\n",
                ucRtc.year, ucRtc.month, ucRtc.date, ucRtc.hour, ucRtc.minute, ucRtc.second, config_data.limit);
            result = f_write(&log_file, write_buffer, strlen(write_buffer), &bytes_write);
            if (result != FR_OK || bytes_write != strlen(write_buffer))
            {
                // Usart0Printf("Failed to write to log file: %s\n", storage_param.log_file_name);
                return;
            }
            f_sync(&log_file);
            break;

        case LOG_SAMPLE_START_USART:
            sprintf(write_buffer, "20%02d-%02d-%02d %02d:%02d:%02d sample start - cycle %ds (command)\r\n",
                ucRtc.year, ucRtc.month, ucRtc.date, ucRtc.hour, ucRtc.minute, ucRtc.second, sample_cycle);
            result = f_write(&log_file, write_buffer, strlen(write_buffer), &bytes_write);
            if (result != FR_OK || bytes_write != strlen(write_buffer))
            {
                // Usart0Printf("Failed to write to log file: %s\n", storage_param.log_file_name);
                return;
            }
            f_sync(&log_file);
            break;

        case LOG_SAMPLE_START_BTN:
            sprintf(write_buffer, "20%02d-%02d-%02d %02d:%02d:%02d sample start - cycle %ds (key press)\r\n",
                ucRtc.year, ucRtc.month, ucRtc.date, ucRtc.hour, ucRtc.minute, ucRtc.second, sample_cycle);
            result = f_write(&log_file, write_buffer, strlen(write_buffer), &bytes_write);
            if (result != FR_OK || bytes_write != strlen(write_buffer))
            {
                // Usart0Printf("Failed to write to log file: %s\n", storage_param.log_file_name);
                return;
            }
            f_sync(&log_file);
            break;

        case LOG_CYCLE_CONFIG:
            sprintf(write_buffer, "20%02d-%02d-%02d %02d:%02d:%02d cycle switch to %ds (key press)\r\n",
                ucRtc.year, ucRtc.month, ucRtc.date, ucRtc.hour, ucRtc.minute, ucRtc.second, sample_cycle);
            result = f_write(&log_file, write_buffer, strlen(write_buffer), &bytes_write);
            if (result != FR_OK || bytes_write != strlen(write_buffer))
            {
                // Usart0Printf("Failed to write to log file: %s\n", storage_param.log_file_name);
                return;
            }
            f_sync(&log_file);
            break;

        case LOG_SAMPLE_STOP_USART:
            sprintf(write_buffer, "20%02d-%02d-%02d %02d:%02d:%02d sample stop (command)\r\n",
                ucRtc.year, ucRtc.month, ucRtc.date, ucRtc.hour, ucRtc.minute, ucRtc.second);
            result = f_write(&log_file, write_buffer, strlen(write_buffer), &bytes_write);
            if (result != FR_OK || bytes_write != strlen(write_buffer))
            {
                // Usart0Printf("Failed to write to log file: %s\n", storage_param.log_file_name);
                return;
            }
            f_sync(&log_file);
            break;

        case LOG_SAMPLE_STOP_BTN:
            sprintf(write_buffer, "20%02d-%02d-%02d %02d:%02d:%02d sample stop (key press)\r\n",
                ucRtc.year, ucRtc.month, ucRtc.date, ucRtc.hour, ucRtc.minute, ucRtc.second);
            result = f_write(&log_file, write_buffer, strlen(write_buffer), &bytes_write);
            if (result != FR_OK || bytes_write != strlen(write_buffer))
            {
                // Usart0Printf("Failed to write to log file: %s\n", storage_param.log_file_name);
                return;
            }
            f_sync(&log_file);
            break;

        case LOG_HIDE_DATA:
            sprintf(write_buffer, "20%02d-%02d-%02d %02d:%02d:%02d hide data\r\n",
                ucRtc.year, ucRtc.month, ucRtc.date, ucRtc.hour, ucRtc.minute, ucRtc.second);
            result = f_write(&log_file, write_buffer, strlen(write_buffer), &bytes_write);
            if (result != FR_OK || bytes_write != strlen(write_buffer))
            {
                // Usart0Printf("Failed to write to log file: %s\n", storage_param.log_file_name);
                return;
            }
            f_sync(&log_file);
            break;

        case LOG_UNHIDE_DATA:
            sprintf(write_buffer, "20%02d-%02d-%02d %02d:%02d:%02d unhide data\r\n",
                ucRtc.year, ucRtc.month, ucRtc.date, ucRtc.hour, ucRtc.minute, ucRtc.second);
            result = f_write(&log_file, write_buffer, strlen(write_buffer), &bytes_write);
            if (result != FR_OK || bytes_write != strlen(write_buffer))
            {
                // Usart0Printf("Failed to write to log file: %s\n", storage_param.log_file_name);
                return;
            }
            f_sync(&log_file);
            break;
    }
}
