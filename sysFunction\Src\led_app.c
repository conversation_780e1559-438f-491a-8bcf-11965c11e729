#include "led_app.h"

void LedTask(void)
{
    static uint32_t breathCounter = 0;  // 呼吸计数器
    static uint8_t pwmCounter = 0;  // PWM计数器
    static uint8_t brightness = 0;  // PWM占空比
    static const uint16_t breathPeriod = 2000;  // 呼吸周期
    static const uint8_t pwmMax = 18;  // PWM周期(精度)

    breathCounter = (breathCounter + 1) % breathPeriod;

    brightness = (uint8_t)((sinf((2.0f * 3.14159f * breathCounter) / breathPeriod) + 1.0f) * pwmMax / 2.0f);

    pwmCounter = (pwmCounter + 1) % pwmMax;
    ucLed[0] = (pwmCounter < brightness) ? 1 : 0;

    LedDisp(ucLed);
}
